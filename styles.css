/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e293b;
    line-height: 1.6;
    min-height: 100vh;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-optical-sizing: auto;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Professional Banking Header Styles */
.banking-header {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 3px solid #1e3a8a;
    box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.header-top-bar {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);
    color: white;
    padding: 0.75rem 0;
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand-section {
    display: flex;
    align-items: center;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1e3a8a;
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-weight: 700;
    font-size: 1.125rem;
    letter-spacing: -0.025em;
    color: #ffffff;
}

.brand-division {
    font-weight: 400;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-text {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.current-time {
    font-family: 'Roboto', monospace;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
}

.header-main {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 2rem 0;
    position: relative;
}

.header-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
}

.title-section {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
}

.title-icon-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.primary-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.25);
    position: relative;
}

.primary-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #1e3a8a, #2563eb, #3b82f6);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.3;
}

.secondary-icons {
    display: flex;
    gap: 0.5rem;
}

.secondary-icons i {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.2);
}

.title-content {
    flex: 1;
}

.main-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.title-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin: 0 0 1rem 0;
    font-weight: 400;
    line-height: 1.5;
}

.title-features {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.feature-badge i {
    font-size: 0.625rem;
    color: #1e3a8a;
}

.header-actions {
    display: flex;
    align-items: center;
}

.quick-stats {
    display: flex;
    gap: 1.5rem;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    min-width: 120px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1e3a8a;
    font-size: 1rem;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    margin-top: 0.125rem;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Upload Section */
.upload-section {
    margin-bottom: 2rem;
}

.upload-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.upload-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a 0%, #2563eb 50%, #3b82f6 100%);
}

.upload-card h2 {
    color: #1e293b;
    margin-bottom: 2rem;
    font-size: 1.625rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.upload-card h2 i {
    color: #1e3a8a;
    font-size: 1.5rem;
}

.upload-area {
    border: 3px dashed #cbd5e1;
    border-radius: 12px;
    padding: 3.5rem 2.5rem;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.6s ease;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(59, 130, 246, 0.15);
}

.upload-area:hover::before {
    left: 100%;
}

.upload-area.dragover {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    transform: scale(1.02);
    box-shadow: 0 16px 35px rgba(16, 185, 129, 0.2);
}

.upload-area.processing {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    pointer-events: none;
}

.upload-icon {
    font-size: 3.5rem;
    color: #3b82f6;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: #2563eb;
}

.upload-area.dragover .upload-icon {
    color: #10b981;
    transform: scale(1.15) rotate(5deg);
}

.upload-content h3 {
    color: #1e293b;
    margin-bottom: 0.75rem;
    font-size: 1.375rem;
    font-weight: 600;
}

.upload-content p {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.browse-link {
    color: #3b82f6;
    cursor: pointer;
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.browse-link:hover {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.file-info {
    color: #64748b;
    font-size: 0.875rem;
    margin-top: 0.75rem;
    font-weight: 500;
}

.upload-status {
    margin-top: 1.5rem;
    padding: 1.25rem;
    border-radius: 10px;
    display: none;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.upload-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.upload-status.success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.success::before {
    background: #10b981;
}

.upload-status.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #fca5a5;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.error::before {
    background: #ef4444;
}

.upload-status.warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border: 1px solid #fcd34d;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.warning::before {
    background: #f59e0b;
}

.upload-status.info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1e40af;
    border: 1px solid #93c5fd;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.info::before {
    background: #3b82f6;
}

.upload-status.enhanced {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
    display: block;
    position: relative;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.enhanced::before {
    background: #10b981;
}

.upload-status.enhanced .status-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.upload-status.enhanced .status-action-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.upload-status.enhanced .status-action-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.upload-status.enhanced .status-action-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.upload-status.enhanced .status-action-btn.secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress indicator for file processing */
.upload-progress {
    margin-top: 1rem;
    display: none;
}

.upload-progress.active {
    display: block;
    animation: slideInUp 0.3s ease-out;
}

.progress-bar-container {
    background: #e2e8f0;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 8px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-align: center;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 2rem;
}

.stats-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    color: #3498db;
    width: 60px;
    text-align: center;
}

.stat-icon.text-success {
    color: #27ae60;
}

.stat-icon.text-danger {
    color: #e74c3c;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Data Section */
.data-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e1e5e9;
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.data-header h2 {
    color: #2c3e50;
    font-size: 1.5rem;
}

.data-controls {
    display: flex;
    gap: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 2px solid #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-outline:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled::before {
    display: none;
}

.btn-sm {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1.125rem 2rem;
    font-size: 1rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    background: white;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.data-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 1.25rem 1rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.data-table th:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #1e3a8a;
}

.data-table th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.data-table th.sortable:hover::after {
    opacity: 0.7;
}

.data-table th.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: #3b82f6;
}

.data-table th.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: #3b82f6;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    white-space: nowrap;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.data-table tbody tr {
    transition: all 0.2s ease;
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.data-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.data-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Enhanced cell styling for specific data types */
.data-table .amount-cell {
    font-weight: 600;
    font-family: 'Roboto', monospace;
    text-align: right;
}

.data-table .date-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
}

.data-table .status-cell {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.data-table .id-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
    font-size: 0.8rem;
}

/* Table search and filter bar */
.table-controls {
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.table-search {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.table-search input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #f9fafb;
}

.table-search input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.table-search i {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 0.875rem;
}

.table-filter-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.table-filter-select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background: #f9fafb;
    transition: all 0.2s ease;
    min-width: 120px;
}

.table-filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.page-info {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #e74c3c;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.close-btn:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    white-space: pre-line;
    line-height: 1.5;
    margin: 0;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e1e5e9;
    text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    /* Banking Header Responsive */
    .header-container {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .header-top-bar .header-container {
        flex-direction: row;
        justify-content: space-between;
    }

    .brand-logo {
        gap: 0.75rem;
    }

    .logo-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .brand-name {
        font-size: 1rem;
    }

    .header-info {
        gap: 1rem;
    }

    .title-section {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .title-icon-group {
        flex-direction: row;
        gap: 1rem;
    }

    .primary-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .main-title {
        font-size: 1.75rem;
    }

    .title-subtitle {
        font-size: 0.875rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .quick-stats {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .quick-stat-item {
        min-width: auto;
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .data-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-controls {
        justify-content: center;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
}

/* Utility Classes */
.text-success {
    color: #27ae60 !important;
}

.text-danger {
    color: #e74c3c !important;
}

.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

/* Navigation Tabs */
.nav-tabs {
    background: white;
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    gap: 0;
}

.nav-tab {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.nav-tab:hover {
    color: #3498db;
    background: #f8f9fa;
}

.nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: #f8f9fa;
}

.alert-badge {
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
}

/* View Containers */
.view-container {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Alert Summary Section */
.alert-summary-section {
    margin-bottom: 2rem;
}

.alert-summary-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.alert-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.alert-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-stat-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
    border-radius: 50%;
    padding: 0.75rem;
}

.alert-stat-icon.new {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.alert-stat-icon.reviewed {
    color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

.alert-stat-icon.dismissed {
    color: #95a5a6;
    background: rgba(149, 165, 166, 0.1);
}

.alert-stat-icon.total {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.alert-stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.alert-stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Alert Controls Section */
.alert-controls-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alert-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-controls-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.alert-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.filter-select,
.filter-input {
    padding: 0.5rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Alerts List Section */
.alerts-list-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alerts-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.alerts-container {
    min-height: 200px;
}

.no-alerts-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #7f8c8d;
}

.no-alerts-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #bdc3c7;
}

.no-alerts-message h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Alert Item Styles */
.alert-item {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.alert-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e2e8f0;
    transition: all 0.3s ease;
}

.alert-item.status-new::before {
    background: linear-gradient(180deg, #ef4444 0%, #dc2626 100%);
}

.alert-item.status-reviewed::before {
    background: linear-gradient(180deg, #f59e0b 0%, #d97706 100%);
}

.alert-item.status-dismissed::before {
    background: linear-gradient(180deg, #6b7280 0%, #4b5563 100%);
}

.alert-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    border-color: #3b82f6;
    transform: translateY(-2px);
}

.alert-item.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.alert-item.selected::before {
    background: linear-gradient(180deg, #3b82f6 0%, #2563eb 100%);
    width: 6px;
}

.alert-item-header {
    padding: 1.75rem;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1.25rem;
}

.alert-type {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: #1e293b;
    font-size: 1.125rem;
}

.alert-type i {
    color: #ef4444;
    font-size: 1.25rem;
}

.alert-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.075em;
    border: 2px solid;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.alert-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

.alert-status.new {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #991b1b;
    border-color: #fca5a5;
}

.alert-status.reviewed {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border-color: #fcd34d;
}

.alert-status.dismissed {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    color: #4b5563;
    border-color: #d1d5db;
}

.alert-status.dismissed::before {
    animation: none;
    opacity: 0.5;
}

.alert-item-body {
    padding: 1.75rem;
}

.alert-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.alert-info-item:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: #e2e8f0;
}

.alert-info-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.075em;
}

.alert-info-value {
    font-weight: 700;
    color: #1e293b;
    font-size: 0.875rem;
    font-family: 'Roboto', monospace;
}

.alert-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.alert-item-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-checkbox {
    margin-right: 0.5rem;
}

/* Alert Pagination */
.alert-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Alert Modal Styles */
.alert-modal-content {
    max-width: 900px;
    width: 95%;
}

.alert-detail-content {
    max-height: 70vh;
    overflow-y: auto;
}

.alert-detail-section {
    margin-bottom: 2rem;
}

.alert-detail-section h4 {
    color: #1e293b;
    margin-bottom: 1.25rem;
    font-size: 1.125rem;
    font-weight: 700;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-detail-section h4 i {
    color: #3b82f6;
    font-size: 1rem;
}

/* Alert Detail Tables */
.alert-detail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-detail-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.alert-detail-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
    transition: all 0.2s ease;
}

.alert-detail-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.alert-detail-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.3);
}

.alert-detail-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.alert-detail-table .label-cell {
    font-weight: 600;
    color: #64748b;
    width: 35%;
    background: rgba(248, 250, 252, 0.8);
}

.alert-detail-table .value-cell {
    color: #1e293b;
    font-weight: 500;
}

.alert-detail-table .amount-cell {
    font-family: 'Roboto', monospace;
    font-weight: 700;
    color: #059669;
}

.alert-detail-table .id-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
    font-size: 0.8rem;
}

.alert-detail-table .status-cell {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.alert-detail-table .date-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
}

/* Transaction Pair Styles */
.transaction-pair-section {
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.transaction-pair-header {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.transaction-pair-header i {
    color: #3b82f6;
    font-size: 1rem;
}

.transaction-pair-content {
    padding: 0;
}

.transaction-detail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    margin: 0;
}

.transaction-detail-table th {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 0.875rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.transaction-detail-table td {
    padding: 0.875rem 1rem;
    border-bottom: 1px solid #f8fafc;
    vertical-align: top;
}

.transaction-detail-table tbody tr:hover {
    background: rgba(248, 250, 252, 0.8);
}

.transaction-detail-table .transaction-type-debit {
    border-left: 4px solid #ef4444;
}

.transaction-detail-table .transaction-type-credit {
    border-left: 4px solid #10b981;
}

.transaction-detail-table .transaction-type-cell {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.transaction-detail-table .transaction-type-cell.debit {
    color: #dc2626;
}

.transaction-detail-table .transaction-type-cell.credit {
    color: #059669;
}

.transaction-detail-table .transaction-type-cell i {
    font-size: 0.875rem;
}

.transaction-detail-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
}

.form-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Responsive Design for Alerts */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-tab {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .alert-stats-grid {
        grid-template-columns: 1fr;
    }

    .alert-filters {
        grid-template-columns: 1fr;
    }

    .alert-controls-header {
        flex-direction: column;
        align-items: stretch;
    }

    .alert-actions {
        justify-content: center;
    }

    .alerts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
    }

    .alert-item-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-info-grid {
        grid-template-columns: 1fr;
    }

    .alert-actions-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-item-actions {
        justify-content: center;
    }

    .transaction-details-grid {
        grid-template-columns: 1fr;
    }

    .alert-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .column-mapping-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .column-mapping-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Rule Configuration Responsive */
    .rule-info-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .current-rule-display {
        padding: 1rem;
    }

    .rule-config-form-section,
    .current-rule-section {
        padding: 1rem;
    }
}

/* Additional Utility Classes for Alerts */
.text-warning {
    color: #f39c12 !important;
}

.text-muted {
    color: #7f8c8d !important;
}

.border-left-danger {
    border-left: 4px solid #e74c3c !important;
}

.border-left-success {
    border-left: 4px solid #27ae60 !important;
}

.border-left-warning {
    border-left: 4px solid #f39c12 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.font-weight-bold {
    font-weight: 600 !important;
}

.small {
    font-size: 0.8rem !important;
}

/* Animation for alert notifications */
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.alert-badge.pulse {
    animation: alertPulse 1s ease-in-out infinite;
}

/* Loading state for alerts */
.alerts-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    color: #7f8c8d;
}

.alerts-loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Alert Notes Table Styles */
.alert-notes-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-notes-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.alert-notes-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
}

.alert-notes-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.alert-notes-table .note-content-cell {
    color: #1e293b;
    line-height: 1.5;
}

.alert-notes-table .note-timestamp-cell {
    color: #64748b;
    font-family: 'Roboto', monospace;
    font-size: 0.8rem;
    white-space: nowrap;
    width: 180px;
}

/* Responsive adjustments for alert detail tables */
@media (max-width: 768px) {
    .alert-modal-content {
        max-width: 95%;
        margin: 1rem;
    }

    .alert-detail-table .label-cell {
        width: 40%;
    }

    .transaction-detail-table {
        font-size: 0.8rem;
    }

    .transaction-detail-table th,
    .transaction-detail-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Enhanced button styles for alerts */
.btn-sm.btn-primary {
    background: #3498db;
    border-color: #3498db;
}

.btn-sm.btn-primary:hover {
    background: #2980b9;
    border-color: #2980b9;
}

.btn-sm.btn-secondary {
    background: #27ae60;
    border-color: #27ae60;
}

.btn-sm.btn-secondary:hover {
    background: #229954;
    border-color: #229954;
}

.btn-sm.btn-outline {
    background: transparent;
    color: #7f8c8d;
    border-color: #7f8c8d;
}

.btn-sm.btn-outline:hover {
    background: #7f8c8d;
    color: white;
    border-color: #7f8c8d;
}

/* Alert item hover effects */
.alert-item:hover .alert-item-actions .btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Checkbox styling */
.alert-checkbox {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    accent-color: #3498db;
}

/* Status badge animations */
.alert-status {
    transition: all 0.3s ease;
}

.alert-status.new {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Enhanced modal styles for alerts */
.alert-modal-content .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.alert-modal-content .modal-footer {
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

/* Column Mapping Modal Styles */
.column-mapping-modal-content {
    max-width: 900px;
    width: 95%;
}

.column-mapping-info {
    padding: 0;
}

.mapping-notice {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.mapping-notice i {
    color: #3498db;
    font-size: 1.2rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.mapping-notice p {
    margin: 0;
    color: #2c3e50;
    line-height: 1.5;
}

.column-mapping-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.mapping-column {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
}

.mapping-column h4 {
    color: #2c3e50;
    margin: 0 0 1rem 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mapping-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mapping-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e1e5e9;
    font-size: 0.9rem;
    color: #2c3e50;
}

.mapping-list li:last-child {
    border-bottom: none;
}

.mapping-list li.found {
    color: #27ae60;
    font-weight: 500;
}

.mapping-list li.missing {
    color: #e74c3c;
    font-style: italic;
}

.mapping-actions {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e1e5e9;
}

.mapping-action-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #2c3e50;
}

.mapping-action-item:last-child {
    margin-bottom: 0;
}

.mapping-action-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

/* Transaction detail cards enhancement */
.transaction-detail-card {
    transition: all 0.3s ease;
}

.transaction-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Filter section enhancements */
.filter-group input:focus,
.filter-group select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

/* Alert statistics cards hover effect */
.alert-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Navigation tab enhancements */
.nav-tab {
    position: relative;
    overflow: hidden;
}

.nav-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-tab:hover::before {
    left: 100%;
}

/* Improved scrollbar for modal content */
.alert-detail-content::-webkit-scrollbar {
    width: 6px;
}

.alert-detail-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Rule Configuration Styles */
.rule-config-header-section {
    margin-bottom: 2rem;
}

.rule-config-header-section h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.rule-config-description {
    color: #7f8c8d;
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

/* Current Rule Display */
.current-rule-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.current-rule-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.current-rule-display {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1.5rem;
    border-left: 4px solid #3498db;
}

.rule-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.rule-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.rule-info-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
}

.rule-info-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

/* Rule Configuration Form */
.rule-config-form-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.rule-config-form-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.rule-config-form {
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-input,
.form-select {
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
    transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-input.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    line-height: 1.4;
}

.form-error {
    font-size: 0.8rem;
    color: #e74c3c;
    font-weight: 500;
    display: none;
}

.form-error.show {
    display: block;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    accent-color: #3498db;
}

.checkbox-group label {
    cursor: pointer;
    margin: 0;
    font-weight: 500;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e1e5e9;
}

/* Rule Status Section */
.rule-status-section {
    margin-bottom: 2rem;
}

.rule-status-message {
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rule-status-message.success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.rule-status-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.rule-status-message.loading {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.rule-status-message i {
    font-size: 1.1rem;
}

/* Transaction Pair Sections */
.transaction-pair-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.transaction-pair-section h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.transaction-pair-section:nth-child(even) {
    background: #ffffff;
    border-left-color: #27ae60;
}

.transaction-pair-section:nth-child(odd) {
    background: #f8f9fa;
    border-left-color: #3498db;
}

/* Enhanced transaction detail cards for multiple pairs */
.transaction-pair-section .transaction-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.transaction-pair-section .transaction-detail-card {
    margin-bottom: 0;
}

/* Responsive design for transaction pairs */
@media (max-width: 768px) {
    .transaction-pair-section .transaction-details-grid {
        grid-template-columns: 1fr;
    }

    .transaction-pair-section {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .transaction-pair-section h5 {
        font-size: 0.9rem;
    }
}
