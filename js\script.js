/**
 * AML/CFT Transaction Monitoring Compliance Division
 * Transaction Analysis Dashboard - Main JavaScript Module
 *
 * Professional banking-grade application for transaction analysis,
 * alert management, and compliance monitoring.
 *
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 * @description Main JavaScript module for the Transaction Analysis Dashboard
 * @features Excel file processing, alert generation, rule configuration, data visualization
 */

// Global variables
let transactionData = [];
let filteredTransactionData = [];
let currentPage = 1;
const rowsPerPage = 50;
let currentSort = { column: null, direction: 'asc' };
let currentFilters = { search: '', drCr: '', branch: '' };

// Alert management variables
let alertsData = [];
let currentAlertPage = 1;
const alertsPerPage = 10;
let filteredAlerts = [];
let selectedAlerts = new Set();
let currentAlertFilters = {
    status: 'all',
    severity: 'all',
    dateFrom: '',
    dateTo: '',
    customer: ''
};

// Column mapping variables
let lastColumnMapping = null;
let lastFileHeaders = null;
let hasColumnOrderIssue = false;

// Alert configuration constants (now configurable)
let alertConfig = {
    minimumAmountThreshold: 300000,
    timeWindowDays: 2,
    requireCounterPartyMatching: true,
    enableAlertConsolidation: true
};

// Default configuration for reset functionality
const DEFAULT_ALERT_CONFIG = {
    minimumAmountThreshold: 300000,
    timeWindowDays: 2,
    requireCounterPartyMatching: true,
    enableAlertConsolidation: true
};

// Required column names in exact order
const requiredColumns = [
    'Transaction ID',
    'Trans Ref No',
    'Source System',
    'UCIC',
    'Customer Id',
    'Customer Name',
    'Account No',
    'Account Open Date',
    'Product Type',
    'Product Sub-type',
    'Branch',
    'Date',
    'Tran Amount',
    'Account Balance',
    'Original Amount',
    'Tran Currency',
    'Dr or Cr',
    'Quantity',
    'Unit Price',
    'Transaction Type',
    'Channel Type',
    'Transaction Sub Type',
    'Channel Sub Type',
    'Instrument Type',
    'Instrument No',
    'Purpose Code',
    'Merchant Type',
    'Merchant ID',
    'Counter Party Name',
    'Counter Customer ID',
    'Counter Account No.',
    'Counter Bank',
    'Counter Country',
    'Remarks',
    'Particulars',
    'Transaction Location Id',
    'Approved User Id',
    'Entry User Id',
    'Posted User Id'
];

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const browseLink = document.getElementById('browseLink');
const uploadStatus = document.getElementById('uploadStatus');
const uploadProgress = document.getElementById('uploadProgress');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const loadingOverlay = document.getElementById('loadingOverlay');
const statsSection = document.getElementById('statsSection');
const dataSection = document.getElementById('dataSection');
const dataTableBody = document.getElementById('dataTableBody');
const pagination = document.getElementById('pagination');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const pageInfo = document.getElementById('pageInfo');
const exportBtn = document.getElementById('exportBtn');
const clearBtn = document.getElementById('clearBtn');
const errorModal = document.getElementById('errorModal');
const errorMessage = document.getElementById('errorMessage');
const closeErrorModal = document.getElementById('closeErrorModal');
const errorOkBtn = document.getElementById('errorOkBtn');

// Table controls
const tableSearchInput = document.getElementById('tableSearchInput');
const drCrFilter = document.getElementById('drCrFilter');
const branchFilter = document.getElementById('branchFilter');
const clearTableFiltersBtn = document.getElementById('clearTableFiltersBtn');

// Navigation and Alert DOM elements
const transactionsTab = document.getElementById('transactionsTab');
const alertsTab = document.getElementById('alertsTab');
const ruleConfigTab = document.getElementById('ruleConfigTab');
const transactionsView = document.getElementById('transactionsView');
const alertsView = document.getElementById('alertsView');
const ruleConfigView = document.getElementById('ruleConfigView');
const alertBadge = document.getElementById('alertBadge');

// Alert statistics elements
const newAlertsCount = document.getElementById('newAlertsCount');
const reviewedAlertsCount = document.getElementById('reviewedAlertsCount');
const dismissedAlertsCount = document.getElementById('dismissedAlertsCount');
const totalAlertsCount = document.getElementById('totalAlertsCount');

// Alert controls elements
const statusFilter = document.getElementById('statusFilter');
const severityFilter = document.getElementById('severityFilter');
const dateFromFilter = document.getElementById('dateFromFilter');
const dateToFilter = document.getElementById('dateToFilter');
const customerFilter = document.getElementById('customerFilter');
const applyFiltersBtn = document.getElementById('applyFiltersBtn');
const clearFiltersBtn = document.getElementById('clearFiltersBtn');
const exportAlertsBtn = document.getElementById('exportAlertsBtn');
const clearAlertsBtn = document.getElementById('clearAlertsBtn');

// Alert list elements
const alertsContainer = document.getElementById('alertsContainer');
const noAlertsMessage = document.getElementById('noAlertsMessage');
const bulkActions = document.getElementById('bulkActions');
const bulkReviewBtn = document.getElementById('bulkReviewBtn');
const bulkDismissBtn = document.getElementById('bulkDismissBtn');
const alertPagination = document.getElementById('alertPagination');
const alertPrevBtn = document.getElementById('alertPrevBtn');
const alertNextBtn = document.getElementById('alertNextBtn');
const alertPageInfo = document.getElementById('alertPageInfo');

// Alert modal elements
const alertDetailModal = document.getElementById('alertDetailModal');
const alertDetailContent = document.getElementById('alertDetailContent');
const closeAlertModal = document.getElementById('closeAlertModal');
const markReviewedBtn = document.getElementById('markReviewedBtn');
const dismissAlertBtn = document.getElementById('dismissAlertBtn');
const closeAlertDetailBtn = document.getElementById('closeAlertDetailBtn');

// Alert notes modal elements
const alertNotesModal = document.getElementById('alertNotesModal');
const alertNoteText = document.getElementById('alertNoteText');
const closeNotesModal = document.getElementById('closeNotesModal');
const cancelNoteBtn = document.getElementById('cancelNoteBtn');
const saveNoteBtn = document.getElementById('saveNoteBtn');

// Column mapping modal elements
const columnMappingModal = document.getElementById('columnMappingModal');
const columnMappingContent = document.getElementById('columnMappingContent');
const closeColumnMappingModal = document.getElementById('closeColumnMappingModal');
const closeColumnMappingBtn = document.getElementById('closeColumnMappingBtn');
const exportReorderedBtn = document.getElementById('exportReorderedBtn');

// Rule configuration elements
const currentTimeWindow = document.getElementById('currentTimeWindow');
const currentAmountThreshold = document.getElementById('currentAmountThreshold');
const currentCounterPartyMatching = document.getElementById('currentCounterPartyMatching');
const currentConsolidation = document.getElementById('currentConsolidation');
const ruleConfigForm = document.getElementById('ruleConfigForm');
const amountThreshold = document.getElementById('amountThreshold');
const timeWindow = document.getElementById('timeWindow');
const counterPartyMatching = document.getElementById('counterPartyMatching');
const alertConsolidation = document.getElementById('alertConsolidation');
const resetToDefaultsBtn = document.getElementById('resetToDefaultsBtn');
const saveRuleConfigBtn = document.getElementById('saveRuleConfigBtn');
const ruleStatusSection = document.getElementById('ruleStatusSection');
const ruleStatusMessage = document.getElementById('ruleStatusMessage');
const amountThresholdError = document.getElementById('amountThresholdError');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeBankingHeader();
});

function initializeEventListeners() {
    // File upload events
    uploadArea.addEventListener('click', () => fileInput.click());
    browseLink.addEventListener('click', (e) => {
        e.stopPropagation();
        fileInput.click();
    });

    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // Button events
    exportBtn.addEventListener('click', exportData);
    clearBtn.addEventListener('click', clearData);
    prevBtn.addEventListener('click', () => changePage(-1));
    nextBtn.addEventListener('click', () => changePage(1));

    // Table control events
    if (tableSearchInput) {
        tableSearchInput.addEventListener('input', debounce(applyTableFilters, 300));
    }
    if (drCrFilter) {
        drCrFilter.addEventListener('change', applyTableFilters);
    }
    if (branchFilter) {
        branchFilter.addEventListener('change', applyTableFilters);
    }
    if (clearTableFiltersBtn) {
        clearTableFiltersBtn.addEventListener('click', clearTableFilters);
    }

    // Modal events
    closeErrorModal.addEventListener('click', closeModal);
    errorOkBtn.addEventListener('click', closeModal);
    errorModal.addEventListener('click', (e) => {
        if (e.target === errorModal) closeModal();
    });

    // Navigation events
    transactionsTab.addEventListener('click', () => switchView('transactions'));
    alertsTab.addEventListener('click', () => switchView('alerts'));
    ruleConfigTab.addEventListener('click', () => switchView('ruleConfig'));

    // Alert filter events
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', applyAlertFilters);
    }
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearAlertFilters);
    }
    if (statusFilter) {
        statusFilter.addEventListener('change', applyAlertFilters);
    }
    if (severityFilter) {
        severityFilter.addEventListener('change', applyAlertFilters);
    }

    // Alert action events
    exportAlertsBtn.addEventListener('click', exportAlerts);
    clearAlertsBtn.addEventListener('click', clearAllAlerts);
    bulkReviewBtn.addEventListener('click', () => bulkUpdateAlerts('reviewed'));
    bulkDismissBtn.addEventListener('click', () => bulkUpdateAlerts('dismissed'));

    // Alert pagination events
    alertPrevBtn.addEventListener('click', () => changeAlertPage(-1));
    alertNextBtn.addEventListener('click', () => changeAlertPage(1));

    // Alert modal events
    closeAlertModal.addEventListener('click', closeAlertDetailModal);
    closeAlertDetailBtn.addEventListener('click', closeAlertDetailModal);
    markReviewedBtn.addEventListener('click', () => updateCurrentAlertStatus('reviewed'));
    dismissAlertBtn.addEventListener('click', () => updateCurrentAlertStatus('dismissed'));
    alertDetailModal.addEventListener('click', (e) => {
        if (e.target === alertDetailModal) closeAlertDetailModal();
    });

    // Alert notes modal events
    closeNotesModal.addEventListener('click', closeAlertNotesModal);
    cancelNoteBtn.addEventListener('click', closeAlertNotesModal);
    saveNoteBtn.addEventListener('click', saveAlertNote);
    alertNotesModal.addEventListener('click', (e) => {
        if (e.target === alertNotesModal) closeAlertNotesModal();
    });

    // Rule configuration events
    ruleConfigForm.addEventListener('submit', saveRuleConfiguration);
    resetToDefaultsBtn.addEventListener('click', resetToDefaults);
    amountThreshold.addEventListener('input', validateAmountThreshold);

    // Column mapping modal events
    closeColumnMappingModal.addEventListener('click', closeColumnMappingModalHandler);
    closeColumnMappingBtn.addEventListener('click', closeColumnMappingModalHandler);
    exportReorderedBtn.addEventListener('click', exportReorderedData);
    columnMappingModal.addEventListener('click', (e) => {
        if (e.target === columnMappingModal) closeColumnMappingModalHandler();
    });
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    try {
        // Validate file size
        validateFileSize(file);

        // Validate file type
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            showError('Please select a valid Excel file (.xlsx or .xls)');
            return;
        }

        // Start processing with enhanced UI feedback
        uploadArea.classList.add('processing');
        showLoading(true);
        showUploadProgress(0, 'Initializing file processing...');
        showUploadStatus('Processing file...', 'info');
    } catch (error) {
        showError(error.message);
        return;
    }

    const reader = new FileReader();

    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const percentLoaded = Math.round((e.loaded / e.total) * 30); // First 30% for reading
            showUploadProgress(percentLoaded, 'Reading file...');
        }
    };

    reader.onload = function(e) {
        try {
            showUploadProgress(30, 'Parsing Excel data...');
            // Use setTimeout to allow UI to update
            setTimeout(() => {
                parseExcelFile(e.target.result, file.name);
            }, 100);
        } catch (error) {
            hideUploadProgress();
            uploadArea.classList.remove('processing');
            showLoading(false);
            showError('Error reading file: ' + error.message);
        }
    };

    reader.onerror = function() {
        hideUploadProgress();
        uploadArea.classList.remove('processing');
        showLoading(false);
        showError('Error reading file. Please try again.');
    };

    reader.readAsArrayBuffer(file);
}

function parseExcelFile(data, fileName) {
    try {
        showUploadProgress(40, 'Reading Excel workbook...');

        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        showUploadProgress(60, 'Converting data to JSON...');

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length === 0) {
            throw new Error('The Excel file appears to be empty.');
        }

        showUploadProgress(80, 'Validating data structure...');

        // Validate and process data
        validateAndProcessData(jsonData, fileName);

    } catch (error) {
        hideUploadProgress();
        uploadArea.classList.remove('processing');
        showLoading(false);
        showError('Error parsing Excel file: ' + error.message);
    }
}

function validateAndProcessData(rawData, fileName) {
    try {
        // Check if we have at least header row
        if (rawData.length < 1) {
            throw new Error('File must contain at least a header row.');
        }

        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Debug: Log the actual headers found
        console.log('File headers found:', headers);
        console.log('Expected headers:', requiredColumns);

        // Validate required columns
        const missingColumns = validateColumns(headers);
        if (missingColumns.length > 0) {
            // Provide more detailed error message
            const detailedError = `Missing required columns: ${missingColumns.join(', ')}\n\n` +
                `Found ${headers.length} columns in file:\n${headers.map((h, i) => `${i + 1}. "${h}"`).join('\n')}\n\n` +
                `Expected ${requiredColumns.length} columns:\n${requiredColumns.map((h, i) => `${i + 1}. "${h}"`).join('\n')}`;
            throw new Error(detailedError);
        }

        // Process and validate data rows
        const processedData = processDataRows(dataRows, headers);

        if (processedData.length === 0) {
            throw new Error('No valid transaction data found in the file.');
        }

        // Store processed data
        transactionData = processedData;
        filteredTransactionData = [...processedData];
        currentPage = 1;

        showUploadProgress(95, 'Finalizing data processing...');

        // Update UI
        showLoading(false);
        hideUploadProgress();
        uploadArea.classList.remove('processing');

        // Show enhanced success message based on column order
        if (hasColumnOrderIssue) {
            showEnhancedUploadStatus(processedData.length, fileName, true);
        } else {
            showUploadStatus(`Successfully loaded ${processedData.length} transactions from ${fileName}`, 'success');
        }

        updateStatistics();
        populateFilterOptions();
        displayData();
        showSections();

        // Generate alerts
        generateAlerts();
        updateAlertBadge();

        // Trigger header updates
        triggerHeaderUpdate('dataLoaded');

    } catch (error) {
        showLoading(false);
        showError(error.message);
    }
}

function validateColumns(headers) {
    const missingColumns = [];
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Store headers for later use
    lastFileHeaders = [...headers];
    hasColumnOrderIssue = false;

    // Check if we have enough columns
    if (headers.length < requiredColumns.length) {
        throw new Error(`File must contain at least ${requiredColumns.length} columns. Found ${headers.length} columns.`);
    }

    // First, try exact positional matching (preferred)
    let exactMatch = true;
    for (let i = 0; i < requiredColumns.length; i++) {
        const requiredCol = requiredColumns[i].toLowerCase();
        const headerCol = normalizedHeaders[i];

        if (headerCol !== requiredCol) {
            exactMatch = false;
            break;
        }
    }

    if (exactMatch) {
        hasColumnOrderIssue = false;
        return []; // Perfect match, no missing columns
    }

    // If exact positional match fails, check if all required columns exist anywhere
    const foundColumns = new Set();

    for (const requiredCol of requiredColumns) {
        const normalizedRequired = requiredCol.toLowerCase();
        let found = false;

        for (let i = 0; i < normalizedHeaders.length; i++) {
            if (normalizedHeaders[i] === normalizedRequired) {
                foundColumns.add(requiredCol);
                found = true;
                break;
            }
        }

        if (!found) {
            missingColumns.push(`"${requiredCol}"`);
        }
    }

    // If we found all columns but not in the right order, set flag for enhanced UI
    if (missingColumns.length === 0 && !exactMatch) {
        hasColumnOrderIssue = true;
        console.warn('All required columns found but not in the expected order. Processing will continue but column order is recommended for best results.');

        // Store column mapping for display
        lastColumnMapping = createColumnMapping(headers);

        return []; // Allow processing to continue
    }

    return missingColumns;
}

function processDataRows(dataRows, headers) {
    const processedData = [];
    const errors = [];

    // Create column mapping for flexible column order support
    const columnMapping = createColumnMapping(headers);

    dataRows.forEach((row, index) => {
        try {
            // Skip empty rows
            if (!row || row.every(cell => !cell && cell !== 0)) {
                return;
            }

            const processedRow = processRow(row, index + 2, columnMapping); // +2 because we start from row 2 (after header)
            if (processedRow) {
                processedData.push(processedRow);
            }
        } catch (error) {
            errors.push(`Row ${index + 2}: ${error.message}`);
        }
    });

    // Show warnings for data validation errors (but don't stop processing)
    if (errors.length > 0 && errors.length < 10) {
        console.warn('Data validation warnings:', errors);
    }

    return processedData;
}

function createColumnMapping(headers) {
    const mapping = {};
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Map each required column to its actual position in the file
    requiredColumns.forEach(requiredCol => {
        const normalizedRequired = requiredCol.toLowerCase();
        const index = normalizedHeaders.findIndex(h => h === normalizedRequired);
        mapping[requiredCol] = index >= 0 ? index : -1;
    });

    return mapping;
}

function processRow(row, rowNumber, columnMapping) {
    const processedRow = {};

    // Map each column to the processed row using the column mapping
    requiredColumns.forEach((colName) => {
        const columnIndex = columnMapping[colName];
        let value = columnIndex >= 0 ? row[columnIndex] : '';

        // Handle empty values
        if (value === undefined || value === null || value === '') {
            value = '';
        } else {
            value = value.toString().trim();
        }

        // Validate and format specific columns
        switch (colName) {
            case 'Tran Amount':
            case 'Account Balance':
            case 'Original Amount':
            case 'Unit Price':
                processedRow[colName] = validateAndFormatAmount(value, rowNumber);
                break;
            case 'Quantity':
                processedRow[colName] = validateAndFormatQuantity(value, rowNumber);
                break;
            case 'Date':
            case 'Account Open Date':
                processedRow[colName] = validateAndFormatDate(value, rowNumber);
                break;
            case 'Dr or Cr':
                processedRow[colName] = validateDrCr(value, rowNumber);
                break;
            default:
                processedRow[colName] = value;
        }
    });

    return processedRow;
}

function validateAndFormatAmount(value, rowNumber) {
    if (!value) return 0;

    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) {
        console.warn(`Row ${rowNumber}: Invalid amount "${value}", using 0`);
        return 0;
    }

    return numValue;
}

function validateAndFormatQuantity(value, rowNumber) {
    if (!value) return 0;

    // Remove commas and spaces
    const cleanValue = value.toString().replace(/[,\s]/g, '');
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) {
        console.warn(`Row ${rowNumber}: Invalid quantity "${value}", using 0`);
        return 0;
    }

    return numValue;
}

function validateAndFormatDate(value, rowNumber) {
    if (!value) return '';
    
    // Try to parse the date
    let date;
    
    // Handle Excel date serial numbers
    if (typeof value === 'number') {
        date = XLSX.SSF.parse_date_code(value);
        if (date) {
            return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
        }
    }
    
    // Try to parse as regular date string
    date = new Date(value);
    if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
    }
    
    console.warn(`Row ${rowNumber}: Invalid date "${value}"`);
    return value.toString();
}

function validateDrCr(value, rowNumber) {
    if (!value) return '';

    const normalizedValue = value.toString().toLowerCase().trim();

    if (normalizedValue === 'dr' || normalizedValue === 'debit') {
        return 'Dr';
    } else if (normalizedValue === 'cr' || normalizedValue === 'credit') {
        return 'Cr';
    }

    console.warn(`Row ${rowNumber}: Invalid Dr/Cr value "${value}"`);
    return value.toString();
}

function updateStatistics() {
    const totalTransactions = transactionData.length;
    let totalCredits = 0;
    let totalDebits = 0;

    transactionData.forEach(row => {
        const amount = parseFloat(row['Tran Amount']) || 0;
        const drCr = row['Dr or Cr'];

        if (drCr === 'Cr') {
            totalCredits += amount;
        } else if (drCr === 'Dr') {
            totalDebits += amount;
        }
    });

    const netAmount = totalCredits - totalDebits;

    // Update DOM elements
    document.getElementById('totalTransactions').textContent = totalTransactions.toLocaleString();
    document.getElementById('creditAmount').textContent = formatCurrency(totalCredits);
    document.getElementById('debitAmount').textContent = formatCurrency(totalDebits);
    document.getElementById('netAmount').textContent = formatCurrency(netAmount);

    // Update net amount color based on positive/negative
    const netElement = document.getElementById('netAmount');
    netElement.className = netAmount >= 0 ? 'text-success' : 'text-danger';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatNumber(number) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(number);
}

function displayData() {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = filteredTransactionData.slice(startIndex, endIndex);

    // Clear existing table data
    dataTableBody.innerHTML = '';

    // Populate table with current page data
    pageData.forEach(row => {
        const tr = document.createElement('tr');

        requiredColumns.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName];

            // Add appropriate CSS classes for styling
            if (colName === 'Tran Amount' || colName === 'Account Balance' || colName === 'Original Amount' || colName === 'Unit Price') {
                td.classList.add('amount-cell');
                value = formatCurrency(parseFloat(value) || 0);
            } else if (colName === 'Quantity') {
                value = formatNumber(parseFloat(value) || 0);
            } else if (colName === 'Dr or Cr') {
                td.classList.add('status-cell');
                td.className += value === 'Cr' ? ' text-success' : value === 'Dr' ? ' text-danger' : '';
            } else if (colName === 'Date' || colName === 'Account Open Date') {
                td.classList.add('date-cell');
            } else if (colName.includes('ID') || colName.includes('No')) {
                td.classList.add('id-cell');
            }

            td.textContent = value || '';
            tr.appendChild(td);
        });

        dataTableBody.appendChild(tr);
    });

    updatePagination();
    updateTableHeaders();
}

function updatePagination() {
    const totalPages = Math.ceil(filteredTransactionData.length / rowsPerPage);
    const totalRecords = filteredTransactionData.length;
    const startRecord = totalRecords > 0 ? (currentPage - 1) * rowsPerPage + 1 : 0;
    const endRecord = Math.min(currentPage * rowsPerPage, totalRecords);

    // Update page info with more detailed information
    if (totalRecords > 0) {
        pageInfo.textContent = `Showing ${startRecord}-${endRecord} of ${totalRecords} transactions (Page ${currentPage} of ${totalPages})`;
    } else {
        pageInfo.textContent = 'No transactions found';
    }

    // Update button states
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages || totalPages === 0;
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredTransactionData.length / rowsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayData();
    }
}

function showSections() {
    statsSection.style.display = 'block';
    dataSection.style.display = 'block';
}

function hideSections() {
    statsSection.style.display = 'none';
    dataSection.style.display = 'none';
}

function exportData() {
    if (transactionData.length === 0) {
        showError('No data to export');
        return;
    }

    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [requiredColumns];
        transactionData.forEach(row => {
            const rowData = requiredColumns.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Transaction Data');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_analysis_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        showError('Error exporting data: ' + error.message);
    }
}

function clearData() {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
        transactionData = [];
        currentPage = 1;
        dataTableBody.innerHTML = '';
        hideSections();
        showUploadStatus('', '');
        fileInput.value = '';
    }
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showUploadStatus(message, type) {
    uploadStatus.textContent = message;
    uploadStatus.className = `upload-status ${type}`;
    uploadStatus.style.display = message ? 'block' : 'none';
}

function showError(message) {
    errorMessage.textContent = message;
    errorModal.style.display = 'flex';
}

function closeModal() {
    errorModal.style.display = 'none';
}

// Enhanced upload status with column mapping support
function showEnhancedUploadStatus(transactionCount, fileName, hasColumnIssue) {
    if (hasColumnIssue) {
        const statusHtml = `
            <div>
                <strong>✓ Successfully loaded ${transactionCount} transactions from ${fileName}</strong>
                <br>
                <span style="color: #856404; margin-top: 0.5rem; display: inline-block;">
                    <i class="fas fa-info-circle"></i> Column order was automatically adjusted for processing
                </span>
            </div>
            <div class="status-actions">
                <button class="status-action-btn" onclick="showColumnMappingModal()">
                    <i class="fas fa-columns"></i> View Column Mapping
                </button>
                <button class="status-action-btn secondary" onclick="exportReorderedData()">
                    <i class="fas fa-download"></i> Export Standard Format
                </button>
            </div>
        `;

        uploadStatus.innerHTML = statusHtml;
        uploadStatus.className = 'upload-status enhanced';
        uploadStatus.style.display = 'block';
    } else {
        showUploadStatus(`Successfully loaded ${transactionCount} transactions from ${fileName}`, 'success');
    }
}

// Show column mapping modal
function showColumnMappingModal() {
    if (!lastColumnMapping || !lastFileHeaders) {
        showError('Column mapping information is not available.');
        return;
    }

    // Generate column mapping content
    const mappingContent = generateColumnMappingContent();
    columnMappingContent.innerHTML = mappingContent;

    // Show modal
    columnMappingModal.style.display = 'flex';
}

// Generate column mapping content HTML
function generateColumnMappingContent() {
    const fileColumns = lastFileHeaders.map((header, index) => ({
        name: header,
        index: index,
        isRequired: requiredColumns.some(req => req.toLowerCase() === header.toLowerCase().trim())
    }));

    const mappedColumns = requiredColumns.map(reqCol => {
        const mappingIndex = lastColumnMapping[reqCol];
        return {
            required: reqCol,
            found: mappingIndex >= 0,
            fileColumn: mappingIndex >= 0 ? lastFileHeaders[mappingIndex] : null,
            fileIndex: mappingIndex
        };
    });

    return `
        <div class="column-mapping-grid">
            <div class="mapping-column">
                <h4><i class="fas fa-file-excel"></i> Your File Columns (${fileColumns.length})</h4>
                <ul class="mapping-list">
                    ${fileColumns.map((col, index) => `
                        <li class="${col.isRequired ? 'found' : ''}">
                            ${index + 1}. ${col.name}
                            ${col.isRequired ? ' <i class="fas fa-check text-success"></i>' : ''}
                        </li>
                    `).join('')}
                </ul>
            </div>
            <div class="mapping-column">
                <h4><i class="fas fa-list-ol"></i> Required Columns (${requiredColumns.length})</h4>
                <ul class="mapping-list">
                    ${mappedColumns.map((col, index) => `
                        <li class="${col.found ? 'found' : 'missing'}">
                            ${index + 1}. ${col.required}
                            ${col.found ?
                                ` → Column ${col.fileIndex + 1} (${col.fileColumn}) <i class="fas fa-arrow-right"></i>` :
                                ' <i class="fas fa-times text-danger"></i> Not found'
                            }
                        </li>
                    `).join('')}
                </ul>
            </div>
        </div>
    `;
}

// Close column mapping modal
function closeColumnMappingModalHandler() {
    columnMappingModal.style.display = 'none';
}

// Export data with reordered columns
function exportReorderedData() {
    if (transactionData.length === 0) {
        showError('No data to export');
        return;
    }

    try {
        // Create new workbook with standard column order
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format with required column order
        const wsData = [requiredColumns];
        transactionData.forEach(row => {
            const rowData = requiredColumns.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Transaction Data');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_analysis_standard_format_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Data exported in standard format as ${filename}`, 'success');

        // Close modal if open
        closeColumnMappingModalHandler();

    } catch (error) {
        showError('Error exporting data: ' + error.message);
    }
}

// Utility functions for better user experience
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to close modal
    if (e.key === 'Escape') {
        closeModal();
    }

    // Ctrl+E to export (when data is available)
    if (e.ctrlKey && e.key === 'e' && transactionData.length > 0) {
        e.preventDefault();
        exportData();
    }
});

// Add file size validation
function validateFileSize(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        throw new Error('File size too large. Please select a file smaller than 50MB.');
    }
}

// ============================================================================
// BANKING HEADER FUNCTIONALITY
// ============================================================================

// Initialize banking header features
function initializeBankingHeader() {
    updateHeaderTime();
    updateHeaderDataStatus();
    updateHeaderAlertCount();

    // Update time every second
    setInterval(updateHeaderTime, 1000);

    // Update header stats when data changes
    document.addEventListener('dataLoaded', updateHeaderDataStatus);
    document.addEventListener('alertsUpdated', updateHeaderAlertCount);
}

// Update current time in header
function updateHeaderTime() {
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const dateString = now.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
        timeElement.textContent = `${dateString} ${timeString}`;
    }
}

// Update header data status
function updateHeaderDataStatus() {
    const statusElement = document.getElementById('headerDataStatus');
    if (statusElement) {
        if (transactionData.length > 0) {
            statusElement.textContent = `${transactionData.length.toLocaleString()} Records`;
            statusElement.style.color = '#059669';
        } else {
            statusElement.textContent = 'Ready';
            statusElement.style.color = '#6b7280';
        }
    }
}

// Update header alert count
function updateHeaderAlertCount() {
    const alertElement = document.getElementById('headerAlertCount');
    if (alertElement) {
        const newAlerts = alertsData.filter(alert => alert.status === 'new').length;
        alertElement.textContent = newAlerts.toLocaleString();

        if (newAlerts > 0) {
            alertElement.style.color = '#dc2626';
            alertElement.style.fontWeight = '700';
        } else {
            alertElement.style.color = '#059669';
            alertElement.style.fontWeight = '600';
        }
    }
}

// Trigger custom events for header updates
function triggerHeaderUpdate(eventType) {
    document.dispatchEvent(new CustomEvent(eventType));
}

// ============================================================================
// ALERT MANAGEMENT SYSTEM
// ============================================================================

// Generate alerts from transaction data
function generateAlerts() {
    alertsData = [];

    if (transactionData.length === 0) {
        updateAlertStatistics();
        return;
    }

    // Group transactions by customer for two-day analysis
    const customerTransactions = groupTransactionsByCustomer();

    // Detect two-day debit/credit alerts
    detectTwoDayDebitCreditAlerts(customerTransactions);

    // Update alert statistics and display
    updateAlertStatistics();
    applyAlertFilters();

    // Trigger header update
    triggerHeaderUpdate('alertsUpdated');

    console.log(`Generated ${alertsData.length} alerts`);
}

// Group transactions by customer ID for two-day analysis
function groupTransactionsByCustomer() {
    const groups = {};

    transactionData.forEach((transaction, index) => {
        const customerId = transaction['Customer Id'] || '';
        const date = transaction['Date'] || '';
        const amount = parseFloat(transaction['Tran Amount']) || 0;
        const drCr = transaction['Dr or Cr'] || '';
        const counterParty = transaction['Counter Party Name'] || '';

        if (!customerId || !date || amount === 0 || !drCr || !counterParty) {
            return; // Skip invalid transactions
        }

        if (!groups[customerId]) {
            groups[customerId] = {
                customerId,
                transactions: []
            };
        }

        groups[customerId].transactions.push({
            ...transaction,
            originalIndex: index,
            amount,
            drCr,
            counterParty,
            date: new Date(date) // Convert to Date object for easier comparison
        });
    });

    return groups;
}

// Detect two-day debit/credit alerts
function detectTwoDayDebitCreditAlerts(customerGroups) {
    Object.values(customerGroups).forEach(group => {
        const { customerId, transactions } = group;

        // Sort transactions by date for easier processing
        transactions.sort((a, b) => a.date - b.date);

        // Find all two-day periods with matching debit/credit pairs
        const twoDayPeriods = findTwoDayPeriods(transactions);

        // Process each two-day period
        twoDayPeriods.forEach(period => {
            const transactionPairs = findMatchingPairs(period.transactions);

            if (transactionPairs.length > 0) {
                const totalAmount = transactionPairs.reduce((sum, pair) => sum + pair.amount, 0);
                createConsolidatedTwoDayAlert(customerId, period, transactionPairs, totalAmount);
            }
        });
    });
}

// Find all unique two-day periods for a customer's transactions
function findTwoDayPeriods(transactions) {
    const periods = [];
    const processedPeriods = new Set();

    for (let i = 0; i < transactions.length; i++) {
        const currentDate = transactions[i].date;
        const periodKey = formatDateForPeriod(currentDate);

        if (processedPeriods.has(periodKey)) {
            continue;
        }

        // Find all transactions within the configured time window
        const periodTransactions = transactions.filter(transaction => {
            const daysDiff = Math.abs((transaction.date - currentDate) / (1000 * 60 * 60 * 24));
            return daysDiff <= (alertConfig.timeWindowDays - 1); // Within configured days
        });

        if (periodTransactions.length > 1) {
            periods.push({
                startDate: new Date(Math.min(...periodTransactions.map(t => t.date))),
                endDate: new Date(Math.max(...periodTransactions.map(t => t.date))),
                transactions: periodTransactions
            });
            processedPeriods.add(periodKey);
        }
    }

    return periods;
}

// Find matching debit/credit pairs within a two-day period
function findMatchingPairs(transactions) {
    const pairs = [];

    // Group by amount and optionally by counter party
    const groups = {};
    transactions.forEach(transaction => {
        // Only consider transactions that meet the minimum amount threshold
        if (transaction.amount >= alertConfig.minimumAmountThreshold) {
            // Create grouping key based on configuration
            const key = alertConfig.requireCounterPartyMatching
                ? `${transaction.amount}_${transaction.counterParty}`
                : `${transaction.amount}`;

            if (!groups[key]) {
                groups[key] = { debits: [], credits: [] };
            }

            if (transaction.drCr === 'Dr') {
                groups[key].debits.push(transaction);
            } else if (transaction.drCr === 'Cr') {
                groups[key].credits.push(transaction);
            }
        }
    });

    // Find matching pairs
    Object.entries(groups).forEach(([key, { debits, credits }]) => {
        if (debits.length > 0 && credits.length > 0) {
            // Create pairs for each debit/credit combination
            debits.forEach(debit => {
                credits.forEach(credit => {
                    // Double-check that both transactions meet the threshold
                    if (debit.amount >= alertConfig.minimumAmountThreshold && credit.amount >= alertConfig.minimumAmountThreshold) {
                        pairs.push({
                            debitTransaction: debit,
                            creditTransaction: credit,
                            amount: debit.amount,
                            counterParty: debit.counterParty
                        });
                    }
                });
            });
        }
    });

    return pairs;
}

// Helper function to format date for period grouping
function formatDateForPeriod(date) {
    return date.toISOString().split('T')[0];
}

// Create a consolidated two-day debit/credit alert
function createConsolidatedTwoDayAlert(customerId, period, transactionPairs, totalAmount) {
    const customerName = transactionPairs[0].debitTransaction['Customer Name'] || '';
    const pairCount = transactionPairs.length;
    const startDate = formatDateForDisplay(period.startDate);
    const endDate = formatDateForDisplay(period.endDate);
    const dateRange = startDate === endDate ? startDate : `${startDate} to ${endDate}`;

    const timeWindowText = alertConfig.timeWindowDays === 1 ? 'same day' : `${alertConfig.timeWindowDays} days`;
    const counterPartyText = alertConfig.requireCounterPartyMatching ? ' with same counter party' : '';
    const thresholdText = formatCurrency(alertConfig.minimumAmountThreshold);

    const alert = {
        id: generateAlertId(),
        type: 'configurable_debit_credit_consolidated',
        title: `${alertConfig.timeWindowDays === 1 ? 'Same-Day' : alertConfig.timeWindowDays + '-Day'} High-Value Debit/Credit Alert`,
        description: `Customer has ${pairCount} matching high-value debit/credit transaction pair${pairCount > 1 ? 's' : ''} (≥${thresholdText} each)${counterPartyText} within ${timeWindowText} (${dateRange}) (Total: ${formatCurrency(totalAmount)})`,
        severity: pairCount > 2 ? 'high' : 'medium',
        status: 'new',
        customerId: customerId,
        customerName: customerName,
        dateRange: dateRange,
        startDate: startDate,
        endDate: endDate,
        totalAmount: totalAmount,
        pairCount: pairCount,
        transactionPairs: transactionPairs,
        timestamp: new Date().toISOString(),
        notes: []
    };

    alertsData.push(alert);
}

// Helper function to format date for display
function formatDateForDisplay(date) {
    return date.toISOString().split('T')[0];
}

// Generate unique alert ID
function generateAlertId() {
    return 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Navigation functions
function switchView(view) {
    // Remove active class from all tabs
    transactionsTab.classList.remove('active');
    alertsTab.classList.remove('active');
    ruleConfigTab.classList.remove('active');

    // Hide all views
    transactionsView.style.display = 'none';
    alertsView.style.display = 'none';
    ruleConfigView.style.display = 'none';

    if (view === 'transactions') {
        transactionsTab.classList.add('active');
        transactionsView.style.display = 'block';
    } else if (view === 'alerts') {
        alertsTab.classList.add('active');
        alertsView.style.display = 'block';

        // Refresh alerts display when switching to alerts view
        applyAlertFilters();
    } else if (view === 'ruleConfig') {
        ruleConfigTab.classList.add('active');
        ruleConfigView.style.display = 'block';

        // Update rule configuration display when switching to rule config view
        updateRuleConfigDisplay();
    }
}

// Update alert badge
function updateAlertBadge() {
    const newAlerts = alertsData.filter(alert => alert.status === 'new').length;

    if (newAlerts > 0) {
        alertBadge.textContent = newAlerts;
        alertBadge.style.display = 'inline-block';
        alertBadge.classList.add('pulse');
    } else {
        alertBadge.style.display = 'none';
        alertBadge.classList.remove('pulse');
    }
}

// Update alert statistics
function updateAlertStatistics() {
    const stats = {
        new: alertsData.filter(alert => alert.status === 'new').length,
        reviewed: alertsData.filter(alert => alert.status === 'reviewed').length,
        dismissed: alertsData.filter(alert => alert.status === 'dismissed').length,
        total: alertsData.length
    };

    newAlertsCount.textContent = stats.new;
    reviewedAlertsCount.textContent = stats.reviewed;
    dismissedAlertsCount.textContent = stats.dismissed;
    totalAlertsCount.textContent = stats.total;
}

// Alert filtering functions
function applyAlertFilters() {
    const filters = {
        status: statusFilter.value,
        severity: severityFilter.value,
        dateFrom: dateFromFilter.value,
        dateTo: dateToFilter.value,
        customer: customerFilter.value.trim().toLowerCase()
    };

    currentAlertFilters = filters;

    filteredAlerts = alertsData.filter(alert => {
        // Status filter
        if (filters.status !== 'all' && alert.status !== filters.status) {
            return false;
        }

        // Severity filter
        if (filters.severity !== 'all' && alert.severity !== filters.severity) {
            return false;
        }

        // Date range filter - handle both single date and date range alerts
        const alertStartDate = alert.startDate || alert.date;
        const alertEndDate = alert.endDate || alert.date;

        if (filters.dateFrom && alertEndDate < filters.dateFrom) {
            return false;
        }
        if (filters.dateTo && alertStartDate > filters.dateTo) {
            return false;
        }

        // Customer filter
        if (filters.customer && !alert.customerId.toLowerCase().includes(filters.customer)) {
            return false;
        }

        return true;
    });

    currentAlertPage = 1;
    displayAlerts();
    updateAlertPagination();
}

function clearAlertFilters() {
    statusFilter.value = 'all';
    severityFilter.value = 'all';
    dateFromFilter.value = '';
    dateToFilter.value = '';
    customerFilter.value = '';
    applyAlertFilters();
}

// Display alerts
function displayAlerts() {
    const startIndex = (currentAlertPage - 1) * alertsPerPage;
    const endIndex = startIndex + alertsPerPage;
    const pageAlerts = filteredAlerts.slice(startIndex, endIndex);

    alertsContainer.innerHTML = '';

    if (pageAlerts.length === 0) {
        alertsContainer.appendChild(noAlertsMessage);
        alertPagination.style.display = 'none';
        bulkActions.style.display = 'none';
        return;
    }

    noAlertsMessage.style.display = 'none';
    alertPagination.style.display = 'flex';

    pageAlerts.forEach(alert => {
        const alertElement = createAlertElement(alert);
        alertsContainer.appendChild(alertElement);
    });

    updateBulkActionsVisibility();
}

// Create alert element
function createAlertElement(alert) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert-item';
    alertDiv.dataset.alertId = alert.id;

    alertDiv.innerHTML = `
        <div class="alert-item-header">
            <div class="alert-type">
                <i class="fas fa-exclamation-triangle"></i>
                ${alert.title}
            </div>
            <div class="alert-status ${alert.status}">${alert.status}</div>
        </div>
        <div class="alert-item-body">
            <div class="alert-info-grid">
                <div class="alert-info-item">
                    <div class="alert-info-label">Customer</div>
                    <div class="alert-info-value">${alert.customerId} - ${alert.customerName}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Date Range</div>
                    <div class="alert-info-value">${alert.dateRange || alert.date || 'N/A'}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Transaction Pairs</div>
                    <div class="alert-info-value">${alert.pairCount || 1} pair${(alert.pairCount || 1) > 1 ? 's' : ''}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Total Amount</div>
                    <div class="alert-info-value">${formatCurrency(alert.totalAmount || alert.amount || 0)}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Severity</div>
                    <div class="alert-info-value text-warning">${alert.severity.toUpperCase()}</div>
                </div>
            </div>
            <div class="alert-actions-row">
                <div class="alert-timestamp">
                    <i class="fas fa-clock"></i> ${formatTimestamp(alert.timestamp)}
                </div>
                <div class="alert-item-actions">
                    <input type="checkbox" class="alert-checkbox" data-alert-id="${alert.id}">
                    <button class="btn btn-sm btn-primary view-details-btn" data-alert-id="${alert.id}">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <button class="btn btn-sm btn-secondary add-note-btn" data-alert-id="${alert.id}">
                        <i class="fas fa-sticky-note"></i> Add Note
                    </button>
                    ${alert.status === 'new' ?
                        `<button class="btn btn-sm btn-outline review-btn" data-alert-id="${alert.id}">
                            <i class="fas fa-eye"></i> Review
                        </button>` : ''
                    }
                    ${alert.status !== 'dismissed' ?
                        `<button class="btn btn-sm btn-outline dismiss-btn" data-alert-id="${alert.id}">
                            <i class="fas fa-times"></i> Dismiss
                        </button>` : ''
                    }
                </div>
            </div>
        </div>
    `;

    // Add click event for selection
    const checkbox = alertDiv.querySelector('.alert-checkbox');
    checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
            selectedAlerts.add(alert.id);
            alertDiv.classList.add('selected');
        } else {
            selectedAlerts.delete(alert.id);
            alertDiv.classList.remove('selected');
        }
        updateBulkActionsVisibility();
    });

    // Add event listeners for action buttons
    const viewDetailsBtn = alertDiv.querySelector('.view-details-btn');
    if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', () => viewAlertDetails(alert.id));
    }

    const addNoteBtn = alertDiv.querySelector('.add-note-btn');
    if (addNoteBtn) {
        addNoteBtn.addEventListener('click', () => addAlertNote(alert.id));
    }

    const reviewBtn = alertDiv.querySelector('.review-btn');
    if (reviewBtn) {
        reviewBtn.addEventListener('click', () => updateAlertStatus(alert.id, 'reviewed'));
    }

    const dismissBtn = alertDiv.querySelector('.dismiss-btn');
    if (dismissBtn) {
        dismissBtn.addEventListener('click', () => updateAlertStatus(alert.id, 'dismissed'));
    }

    return alertDiv;
}

// Format timestamp for display
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// Update bulk actions visibility
function updateBulkActionsVisibility() {
    if (selectedAlerts.size > 0) {
        bulkActions.style.display = 'flex';
    } else {
        bulkActions.style.display = 'none';
    }
}

// Alert pagination
function updateAlertPagination() {
    const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);

    alertPageInfo.textContent = `Page ${currentAlertPage} of ${totalPages}`;
    alertPrevBtn.disabled = currentAlertPage <= 1;
    alertNextBtn.disabled = currentAlertPage >= totalPages;
}

function changeAlertPage(direction) {
    const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);
    const newPage = currentAlertPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentAlertPage = newPage;
        displayAlerts();
        updateAlertPagination();
    }
}

// Alert management functions
function updateAlertStatus(alertId, newStatus) {
    const alert = alertsData.find(a => a.id === alertId);
    if (alert) {
        alert.status = newStatus;
        updateAlertStatistics();
        updateAlertBadge();
        applyAlertFilters(); // Refresh display
    }
}

function bulkUpdateAlerts(newStatus) {
    if (selectedAlerts.size === 0) return;

    selectedAlerts.forEach(alertId => {
        updateAlertStatus(alertId, newStatus);
    });

    selectedAlerts.clear();
    updateBulkActionsVisibility();
}

// Alert detail modal functions
let currentAlertId = null;

function viewAlertDetails(alertId) {
    const alert = alertsData.find(a => a.id === alertId);
    if (!alert) return;

    currentAlertId = alertId;

    const detailContent = `
        <div class="alert-detail-section">
            <h4><i class="fas fa-info-circle"></i> Alert Summary</h4>
            <table class="alert-detail-table">
                <tbody>
                    <tr>
                        <td class="label-cell">Alert Type</td>
                        <td class="value-cell">${alert.title}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Description</td>
                        <td class="value-cell">${alert.description}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Severity</td>
                        <td class="value-cell status-cell" style="color: ${alert.severity === 'high' ? '#dc2626' : alert.severity === 'medium' ? '#d97706' : '#3b82f6'}">${alert.severity.toUpperCase()}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Status</td>
                        <td class="value-cell status-cell" style="color: ${alert.status === 'new' ? '#dc2626' : alert.status === 'reviewed' ? '#d97706' : '#6b7280'}">${alert.status.toUpperCase()}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Alert ID</td>
                        <td class="value-cell id-cell">${alert.id}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Generated</td>
                        <td class="value-cell date-cell">${formatTimestamp(alert.timestamp)}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="alert-detail-section">
            <h4><i class="fas fa-user"></i> Customer Information</h4>
            <table class="alert-detail-table">
                <tbody>
                    <tr>
                        <td class="label-cell">Customer ID</td>
                        <td class="value-cell id-cell">${alert.customerId}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Customer Name</td>
                        <td class="value-cell">${alert.customerName}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Date Range</td>
                        <td class="value-cell date-cell">${alert.dateRange || alert.date || 'N/A'}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Transaction Pairs</td>
                        <td class="value-cell">${alert.pairCount || 1} pair${(alert.pairCount || 1) > 1 ? 's' : ''}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Total Amount</td>
                        <td class="value-cell amount-cell">${formatCurrency(alert.totalAmount || alert.amount || 0)}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="alert-detail-section">
            <h4><i class="fas fa-exchange-alt"></i> Transaction Details</h4>
            ${generateTransactionPairsHTML(alert)}
        </div>

        ${alert.notes.length > 0 ? `
        <div class="alert-detail-section">
            <h4><i class="fas fa-sticky-note"></i> Investigation Notes</h4>
            <table class="alert-notes-table">
                <thead>
                    <tr>
                        <th>Note Content</th>
                        <th>Timestamp</th>
                    </tr>
                </thead>
                <tbody>
                    ${alert.notes.map(note => `
                        <tr>
                            <td class="note-content-cell">${note.content}</td>
                            <td class="note-timestamp-cell">${formatTimestamp(note.timestamp)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}
    `;

    alertDetailContent.innerHTML = detailContent;
    alertDetailModal.style.display = 'flex';
}

function closeAlertDetailModal() {
    alertDetailModal.style.display = 'none';
    currentAlertId = null;
}

function updateCurrentAlertStatus(newStatus) {
    if (currentAlertId) {
        updateAlertStatus(currentAlertId, newStatus);
        closeAlertDetailModal();
    }
}

// Generate HTML for transaction pairs
function generateTransactionPairsHTML(alert) {
    // Handle both old format (single pair) and new format (multiple pairs)
    let pairs = [];

    if (alert.transactionPairs && alert.transactionPairs.length > 0) {
        // New consolidated format
        pairs = alert.transactionPairs;
    } else if (alert.debitTransaction && alert.creditTransaction) {
        // Old format - single pair
        pairs = [{
            debitTransaction: alert.debitTransaction,
            creditTransaction: alert.creditTransaction,
            amount: alert.amount
        }];
    }

    if (pairs.length === 0) {
        return '<p>No transaction details available.</p>';
    }

    return pairs.map((pair, index) => `
        <div class="transaction-pair-section">
            <div class="transaction-pair-header">
                <i class="fas fa-exchange-alt"></i>
                Transaction Pair ${pairs.length > 1 ? (index + 1) : ''} - ${formatCurrency(pair.amount)} - Counter Party: ${pair.counterParty || pair.debitTransaction['Counter Party Name'] || 'N/A'}
            </div>
            <div class="transaction-pair-content">
                <table class="transaction-detail-table">
                    <thead>
                        <tr>
                            <th>Field</th>
                            <th>Debit Transaction</th>
                            <th>Credit Transaction</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="label-cell">Transaction Type</td>
                            <td class="transaction-type-debit">
                                <div class="transaction-type-cell debit">
                                    <i class="fas fa-arrow-down"></i>
                                    DEBIT
                                </div>
                            </td>
                            <td class="transaction-type-credit">
                                <div class="transaction-type-cell credit">
                                    <i class="fas fa-arrow-up"></i>
                                    CREDIT
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">Transaction ID</td>
                            <td class="value-cell id-cell">${pair.debitTransaction['Transaction ID'] || 'N/A'}</td>
                            <td class="value-cell id-cell">${pair.creditTransaction['Transaction ID'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Reference No</td>
                            <td class="value-cell id-cell">${pair.debitTransaction['Trans Ref No'] || 'N/A'}</td>
                            <td class="value-cell id-cell">${pair.creditTransaction['Trans Ref No'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Date</td>
                            <td class="value-cell date-cell">${pair.debitTransaction['Date'] || 'N/A'}</td>
                            <td class="value-cell date-cell">${pair.creditTransaction['Date'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Amount</td>
                            <td class="value-cell amount-cell">${formatCurrency(parseFloat(pair.debitTransaction['Tran Amount']) || 0)}</td>
                            <td class="value-cell amount-cell">${formatCurrency(parseFloat(pair.creditTransaction['Tran Amount']) || 0)}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Account No</td>
                            <td class="value-cell id-cell">${pair.debitTransaction['Account No'] || 'N/A'}</td>
                            <td class="value-cell id-cell">${pair.creditTransaction['Account No'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Customer Name</td>
                            <td class="value-cell">${pair.debitTransaction['Customer Name'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Customer Name'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Counter Party</td>
                            <td class="value-cell">${pair.debitTransaction['Counter Party Name'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Counter Party Name'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Branch</td>
                            <td class="value-cell">${pair.debitTransaction['Branch'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Branch'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Transaction Type</td>
                            <td class="value-cell">${pair.debitTransaction['Transaction Type'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Transaction Type'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Channel Type</td>
                            <td class="value-cell">${pair.debitTransaction['Channel Type'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Channel Type'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Remarks</td>
                            <td class="value-cell">${pair.debitTransaction['Remarks'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Remarks'] || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="label-cell">Particulars</td>
                            <td class="value-cell">${pair.debitTransaction['Particulars'] || 'N/A'}</td>
                            <td class="value-cell">${pair.creditTransaction['Particulars'] || 'N/A'}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `).join('');
}

// Alert notes functions
let currentNoteAlertId = null;

function addAlertNote(alertId) {
    currentNoteAlertId = alertId;
    alertNoteText.value = '';
    alertNotesModal.style.display = 'flex';
}

function closeAlertNotesModal() {
    alertNotesModal.style.display = 'none';
    currentNoteAlertId = null;
    alertNoteText.value = '';
}

function saveAlertNote() {
    if (!currentNoteAlertId || !alertNoteText.value.trim()) return;

    const alert = alertsData.find(a => a.id === currentNoteAlertId);
    if (alert) {
        alert.notes.push({
            content: alertNoteText.value.trim(),
            timestamp: new Date().toISOString()
        });

        closeAlertNotesModal();
        applyAlertFilters(); // Refresh display
    }
}

// Export alerts function
function exportAlerts() {
    if (filteredAlerts.length === 0) {
        showError('No alerts to export');
        return;
    }

    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Prepare alert data for export
        const alertExportData = [
            [
                'Alert ID',
                'Alert Type',
                'Description',
                'Status',
                'Severity',
                'Customer ID',
                'Customer Name',
                'Date Range',
                'Total Amount',
                'Transaction Pairs Count',
                'Transaction Details',
                'Alert Timestamp',
                'Notes'
            ]
        ];

        filteredAlerts.forEach(alert => {
            const notes = alert.notes.map(note => `${formatTimestamp(note.timestamp)}: ${note.content}`).join(' | ');

            // Handle both consolidated and legacy alert formats
            if (alert.transactionPairs && alert.transactionPairs.length > 0) {
                // Consolidated format - create one row with summary and detailed breakdown
                const transactionDetails = alert.transactionPairs.map((pair, index) =>
                    `Pair ${index + 1}: Dr(${pair.debitTransaction['Transaction ID']}-${pair.debitTransaction['Trans Ref No']}) Cr(${pair.creditTransaction['Transaction ID']}-${pair.creditTransaction['Trans Ref No']}) Amount:${formatCurrency(pair.amount)} CounterParty:${pair.counterParty || pair.debitTransaction['Counter Party Name'] || 'N/A'}`
                ).join(' | ');

                alertExportData.push([
                    alert.id,
                    alert.title,
                    alert.description,
                    alert.status,
                    alert.severity,
                    alert.customerId,
                    alert.customerName,
                    alert.dateRange || alert.date || 'N/A',
                    alert.totalAmount || alert.amount || 0,
                    alert.pairCount || 1,
                    transactionDetails,
                    formatTimestamp(alert.timestamp),
                    notes
                ]);
            } else {
                // Legacy format - single pair
                alertExportData.push([
                    alert.id,
                    alert.title,
                    alert.description,
                    alert.status,
                    alert.severity,
                    alert.customerId,
                    alert.customerName,
                    alert.dateRange || alert.date || 'N/A',
                    alert.amount || 0,
                    1,
                    `Dr(${alert.debitTransaction?.['Transaction ID'] || 'N/A'}-${alert.debitTransaction?.['Trans Ref No'] || 'N/A'}) Cr(${alert.creditTransaction?.['Transaction ID'] || 'N/A'}-${alert.creditTransaction?.['Trans Ref No'] || 'N/A'}) CounterParty:${alert.debitTransaction?.['Counter Party Name'] || 'N/A'}`,
                    formatTimestamp(alert.timestamp),
                    notes
                ]);
            }
        });

        const ws = XLSX.utils.aoa_to_sheet(alertExportData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Alerts');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_alerts_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Alerts exported successfully as ${filename}`, 'success');

    } catch (error) {
        showError('Error exporting alerts: ' + error.message);
    }
}

// Clear all alerts function
function clearAllAlerts() {
    if (alertsData.length === 0) {
        showError('No alerts to clear');
        return;
    }

    if (confirm('Are you sure you want to clear all alerts? This action cannot be undone.')) {
        alertsData = [];
        filteredAlerts = [];
        selectedAlerts.clear();
        currentAlertPage = 1;

        updateAlertStatistics();
        updateAlertBadge();
        displayAlerts();
        updateAlertPagination();

        showUploadStatus('All alerts cleared successfully', 'success');
    }
}

// Update clear data function to also clear alerts
const originalClearData = clearData;
function clearData() {
    if (confirm('Are you sure you want to clear all data and alerts? This action cannot be undone.')) {
        // Clear alerts
        alertsData = [];
        filteredAlerts = [];
        selectedAlerts.clear();
        currentAlertPage = 1;
        updateAlertStatistics();
        updateAlertBadge();

        // Clear transaction data
        transactionData = [];
        currentPage = 1;
        dataTableBody.innerHTML = '';
        hideSections();
        showUploadStatus('', '');
        fileInput.value = '';

        // Switch back to transactions view
        switchView('transactions');
    }
}

// ============================================================================
// RULE CONFIGURATION SYSTEM
// ============================================================================

// Update rule configuration display
function updateRuleConfigDisplay() {
    // Update current rule display
    const timeWindowText = alertConfig.timeWindowDays === 1 ? '1 day (same day)' : `${alertConfig.timeWindowDays} days (consecutive days)`;
    currentTimeWindow.textContent = timeWindowText;
    currentAmountThreshold.textContent = formatCurrency(alertConfig.minimumAmountThreshold);
    currentCounterPartyMatching.textContent = alertConfig.requireCounterPartyMatching ? 'Required (exact match)' : 'Not required';
    currentConsolidation.textContent = alertConfig.enableAlertConsolidation ? 'Enabled (one alert per customer per period)' : 'Disabled (separate alerts for each pair)';

    // Update form values
    amountThreshold.value = alertConfig.minimumAmountThreshold;
    timeWindow.value = alertConfig.timeWindowDays;
    counterPartyMatching.checked = alertConfig.requireCounterPartyMatching;
    alertConsolidation.checked = alertConfig.enableAlertConsolidation;

    // Clear any previous status messages
    hideRuleStatus();
}

// Validate amount threshold input
function validateAmountThreshold() {
    const value = parseFloat(amountThreshold.value);
    const errorElement = amountThresholdError;

    // Clear previous error styling
    amountThreshold.classList.remove('error');
    errorElement.classList.remove('show');

    if (isNaN(value) || value <= 0) {
        amountThreshold.classList.add('error');
        errorElement.textContent = 'Amount threshold must be a positive number';
        errorElement.classList.add('show');
        return false;
    }

    if (value > 999999999) {
        amountThreshold.classList.add('error');
        errorElement.textContent = 'Amount threshold is too large';
        errorElement.classList.add('show');
        return false;
    }

    return true;
}

// Save rule configuration
function saveRuleConfiguration(event) {
    event.preventDefault();

    // Validate form
    if (!validateAmountThreshold()) {
        showRuleStatus('Please fix the validation errors before saving.', 'error');
        return;
    }

    // Show loading status
    showRuleStatus('Applying new rule configuration and regenerating alerts...', 'loading');

    // Update configuration
    const newConfig = {
        minimumAmountThreshold: parseFloat(amountThreshold.value),
        timeWindowDays: parseInt(timeWindow.value),
        requireCounterPartyMatching: counterPartyMatching.checked,
        enableAlertConsolidation: alertConsolidation.checked
    };

    // Apply new configuration
    alertConfig = { ...newConfig };

    // Regenerate alerts with new configuration
    setTimeout(() => {
        try {
            // Clear existing alerts
            alertsData = [];
            filteredAlerts = [];
            selectedAlerts.clear();
            currentAlertPage = 1;

            // Regenerate alerts if transaction data exists
            if (transactionData.length > 0) {
                generateAlerts();
                updateAlertBadge();
            }

            // Update displays
            updateRuleConfigDisplay();
            updateAlertStatistics();

            // Show success message
            const alertCount = alertsData.length;
            showRuleStatus(`Rule configuration saved successfully! Generated ${alertCount} alert${alertCount !== 1 ? 's' : ''} with new settings.`, 'success');

        } catch (error) {
            console.error('Error applying rule configuration:', error);
            showRuleStatus('Error applying rule configuration. Please try again.', 'error');
        }
    }, 500); // Small delay to show loading state
}

// Reset to default configuration
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all rule settings to their default values?')) {
        alertConfig = { ...DEFAULT_ALERT_CONFIG };
        updateRuleConfigDisplay();
        showRuleStatus('Rule configuration reset to default values. Click "Save Rule Configuration" to apply changes.', 'success');
    }
}

// Show rule status message
function showRuleStatus(message, type) {
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        loading: 'fas fa-spinner fa-spin'
    };

    ruleStatusMessage.innerHTML = `<i class="${iconMap[type]}"></i> ${message}`;
    ruleStatusMessage.className = `rule-status-message ${type}`;
    ruleStatusSection.style.display = 'block';

    // Auto-hide success and error messages after 5 seconds
    if (type !== 'loading') {
        setTimeout(() => {
            hideRuleStatus();
        }, 5000);
    }
}

// Hide rule status message
function hideRuleStatus() {
    ruleStatusSection.style.display = 'none';
}

// Enhanced UI Functions

// Show upload progress
function showUploadProgress(percentage, message) {
    uploadProgress.classList.add('active');
    progressBar.style.width = percentage + '%';
    progressText.textContent = message;
}

// Hide upload progress
function hideUploadProgress() {
    uploadProgress.classList.remove('active');
    progressBar.style.width = '0%';
    progressText.textContent = 'Processing...';
}

// Debounce function for search input
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Populate filter options
function populateFilterOptions() {
    // Populate branch filter
    const branches = [...new Set(transactionData.map(row => row['Branch']).filter(Boolean))];
    branchFilter.innerHTML = '<option value="">All Branches</option>';
    branches.sort().forEach(branch => {
        const option = document.createElement('option');
        option.value = branch;
        option.textContent = branch;
        branchFilter.appendChild(option);
    });
}

// Apply table filters
function applyTableFilters() {
    const searchTerm = tableSearchInput.value.toLowerCase().trim();
    const drCrValue = drCrFilter.value;
    const branchValue = branchFilter.value;

    currentFilters = { search: searchTerm, drCr: drCrValue, branch: branchValue };

    filteredTransactionData = transactionData.filter(row => {
        // Search filter
        if (searchTerm) {
            const searchableFields = [
                'Customer Name', 'Account No', 'Transaction ID', 'Trans Ref No',
                'Counter Party Name', 'Remarks', 'Particulars'
            ];
            const matchesSearch = searchableFields.some(field =>
                (row[field] || '').toString().toLowerCase().includes(searchTerm)
            );
            if (!matchesSearch) return false;
        }

        // Dr/Cr filter
        if (drCrValue && row['Dr or Cr'] !== drCrValue) {
            return false;
        }

        // Branch filter
        if (branchValue && row['Branch'] !== branchValue) {
            return false;
        }

        return true;
    });

    currentPage = 1;
    displayData();
}

// Clear table filters
function clearTableFilters() {
    tableSearchInput.value = '';
    drCrFilter.value = '';
    branchFilter.value = '';
    currentFilters = { search: '', drCr: '', branch: '' };
    filteredTransactionData = [...transactionData];
    currentPage = 1;
    displayData();
}

// Enhanced table sorting
function sortTable(column) {
    if (currentSort.column === column) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.column = column;
        currentSort.direction = 'asc';
    }

    filteredTransactionData.sort((a, b) => {
        let aVal = a[column] || '';
        let bVal = b[column] || '';

        // Handle numeric columns
        if (['Tran Amount', 'Account Balance', 'Original Amount', 'Unit Price', 'Quantity'].includes(column)) {
            aVal = parseFloat(aVal) || 0;
            bVal = parseFloat(bVal) || 0;
        }
        // Handle date columns
        else if (column === 'Date' || column === 'Account Open Date') {
            aVal = new Date(aVal) || new Date(0);
            bVal = new Date(bVal) || new Date(0);
        }
        // Handle string columns
        else {
            aVal = aVal.toString().toLowerCase();
            bVal = bVal.toString().toLowerCase();
        }

        if (aVal < bVal) return currentSort.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return currentSort.direction === 'asc' ? 1 : -1;
        return 0;
    });

    currentPage = 1;
    displayData();
    updateTableHeaders();
}

// Update table headers with sort indicators
function updateTableHeaders() {
    const headers = document.querySelectorAll('.data-table th');
    headers.forEach((header, index) => {
        const column = requiredColumns[index];
        header.classList.remove('sort-asc', 'sort-desc');
        header.classList.add('sortable');

        if (currentSort.column === column) {
            header.classList.add(currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
        }

        // Add click event for sorting
        header.style.cursor = 'pointer';
        header.onclick = () => sortTable(column);
    });
}
